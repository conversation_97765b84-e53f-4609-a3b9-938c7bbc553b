.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  padding: var(--spacing-4);

  &--visible {
    opacity: 1;
    visibility: visible;
  }
}

.modal-container {
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transform: scale(0.9) translateY(-20px);
  transition: all var(--transition-normal);
  outline: none;

  &--visible {
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
  flex-shrink: 0;
}

.modal-title {
  flex: 1;

  h2 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
  }
}

.modal-close-btn {
  background: none;
  border: none;
  padding: var(--spacing-2);
  cursor: pointer;
  color: var(--color-gray-500);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: var(--color-gray-100);
    color: var(--color-gray-700);
  }

  &:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
  }
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-6);

  &--no-header {
    padding-top: var(--spacing-6);
  }
}

.modal-footer {
  padding: var(--spacing-6);
  border-top: 1px solid var(--color-gray-200);
  flex-shrink: 0;
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
}

// Global body class when modal is open
:global(body.modal-open) {
  overflow: hidden;
}

// Responsive design
@media (max-width: 768px) {
  .modal-overlay {
    padding: var(--spacing-2);
  }

  .modal-container {
    max-height: 95vh;
    width: 100% !important;
    max-width: none !important;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: var(--spacing-4);
  }

  .modal-title h2 {
    font-size: var(--font-size-lg);
  }
}

// Animation keyframes (if not using Angular Animations)
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: scale(0.9) translateY(-20px);
  }
  to {
    transform: scale(1) translateY(0);
  }
}

// Focus styles
.modal-container:focus {
  outline: none;
}

// Scrollbar styling for modal body
.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}
