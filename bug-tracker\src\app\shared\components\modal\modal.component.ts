import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, HostListener, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-modal',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="modal-overlay" 
         [class.modal-overlay--visible]="isVisible"
         (click)="onOverlayClick($event)"
         [@fadeInOut]="isVisible ? 'in' : 'out'">
      
      <div class="modal-container" 
           #modalContainer
           [class.modal-container--visible]="isVisible"
           [@slideInOut]="isVisible ? 'in' : 'out'"
           [style.max-width]="maxWidth"
           [style.width]="width">
        
        <!-- Modal Header -->
        <div class="modal-header" *ngIf="title || showCloseButton">
          <div class="modal-title">
            <h2 *ngIf="title">{{ title }}</h2>
          </div>
          <button 
            type="button" 
            class="modal-close-btn"
            *ngIf="showCloseButton"
            (click)="close()"
            aria-label="Close modal">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        <!-- Modal Body -->
        <div class="modal-body" [class.modal-body--no-header]="!title && !showCloseButton">
          <ng-content></ng-content>
        </div>

        <!-- Modal Footer -->
        <div class="modal-footer" *ngIf="showFooter">
          <ng-content select="[slot=footer]"></ng-content>
        </div>
      </div>
    </div>
  `,
  styleUrl: './modal.component.scss',
  animations: [
    // Add animations here if needed
  ]
})
export class ModalComponent implements OnInit, OnDestroy {
  @Input() isVisible = false;
  @Input() title = '';
  @Input() showCloseButton = true;
  @Input() showFooter = false;
  @Input() closeOnOverlayClick = true;
  @Input() closeOnEscape = true;
  @Input() maxWidth = '600px';
  @Input() width = '90%';
  
  @Output() modalClose = new EventEmitter<void>();
  @Output() modalOpen = new EventEmitter<void>();
  
  @ViewChild('modalContainer', { static: false }) modalContainer!: ElementRef;

  ngOnInit() {
    if (this.isVisible) {
      this.onModalOpen();
    }
  }

  ngOnDestroy() {
    this.removeBodyClass();
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscapeKey(event: KeyboardEvent) {
    if (this.closeOnEscape && this.isVisible) {
      this.close();
    }
  }

  onOverlayClick(event: MouseEvent) {
    if (this.closeOnOverlayClick && event.target === event.currentTarget) {
      this.close();
    }
  }

  open() {
    this.isVisible = true;
    this.onModalOpen();
    this.modalOpen.emit();
  }

  close() {
    this.isVisible = false;
    this.onModalClose();
    this.modalClose.emit();
  }

  private onModalOpen() {
    this.addBodyClass();
    // Focus trap could be added here
    setTimeout(() => {
      if (this.modalContainer?.nativeElement) {
        this.modalContainer.nativeElement.focus();
      }
    }, 100);
  }

  private onModalClose() {
    this.removeBodyClass();
  }

  private addBodyClass() {
    document.body.classList.add('modal-open');
  }

  private removeBodyClass() {
    document.body.classList.remove('modal-open');
  }
}
