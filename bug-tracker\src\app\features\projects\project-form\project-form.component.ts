import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { ProjectService } from '../../../core/services/project.service';
import { UserService } from '../../../core/services/user.service';
import { Project, CreateProjectRequest, UpdateProjectRequest, ProjectStatus } from '../../../core/models/project.model';
import { User, UserRole } from '../../../core/models/user.model';

@Component({
  selector: 'app-project-form',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule],
  template: `
    <div class="page-container">
      <div class="page-header">
        <div class="project-form__header">
          <h1>{{ isEditMode ? 'Edit Project' : 'Create Project' }}</h1>
          <div class="header-actions">
            <button type="button" class="btn btn-outline" (click)="cancel()">Cancel</button>
            <button
              type="submit"
              class="btn btn-primary"
              [disabled]="projectForm.invalid || saving"
              (click)="onSubmit()">
              {{ saving ? 'Saving...' : (isEditMode ? 'Update Project' : 'Create Project') }}
            </button>
          </div>
        </div>
      </div>

      <form [formGroup]="projectForm" (ngSubmit)="onSubmit()" class="project-form">
        <!-- Basic Information -->
        <div class="form-section">
          <div class="section-header">
            <h2>Basic Information</h2>
            <p>Provide the basic details about your project</p>
          </div>

          <div class="form-grid">
            <div class="form-group">
              <label for="name" class="form-label required">Project Name</label>
              <input
                type="text"
                id="name"
                class="form-control"
                formControlName="name"
                placeholder="Enter project name"
                [class.error]="projectForm.get('name')?.invalid && projectForm.get('name')?.touched">
              <div class="form-error" *ngIf="projectForm.get('name')?.invalid && projectForm.get('name')?.touched">
                <span *ngIf="projectForm.get('name')?.errors?.['required']">Project name is required</span>
                <span *ngIf="projectForm.get('name')?.errors?.['minlength']">Project name must be at least 3 characters</span>
              </div>
            </div>

            <div class="form-group">
              <label for="projectUrl" class="form-label required">Project URL</label>
              <input
                type="url"
                id="projectUrl"
                class="form-control"
                formControlName="projectUrl"
                placeholder="https://example.com"
                [class.error]="projectForm.get('projectUrl')?.invalid && projectForm.get('projectUrl')?.touched">
              <div class="form-error" *ngIf="projectForm.get('projectUrl')?.invalid && projectForm.get('projectUrl')?.touched">
                <span *ngIf="projectForm.get('projectUrl')?.errors?.['required']">Project URL is required</span>
                <span *ngIf="projectForm.get('projectUrl')?.errors?.['pattern']">Please enter a valid URL</span>
              </div>
            </div>

            <div class="form-group form-group--full">
              <label for="description" class="form-label required">Description</label>
              <textarea
                id="description"
                class="form-control"
                formControlName="description"
                rows="4"
                placeholder="Describe your project..."
                [class.error]="projectForm.get('description')?.invalid && projectForm.get('description')?.touched"></textarea>
              <div class="form-error" *ngIf="projectForm.get('description')?.invalid && projectForm.get('description')?.touched">
                <span *ngIf="projectForm.get('description')?.errors?.['required']">Description is required</span>
                <span *ngIf="projectForm.get('description')?.errors?.['minlength']">Description must be at least 10 characters</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Modules and Features -->
        <div class="form-section">
          <div class="section-header">
            <h2>Modules & Features</h2>
            <p>Define the modules and features for your project</p>
            <button type="button" class="btn btn-secondary" (click)="addModule()">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <rect x="11" y="4" width="2" height="16" rx="1"></rect>
                <rect x="4" y="11" width="16" height="2" rx="1"></rect>
              </svg>
              Add Module
            </button>
          </div>

          <div formArrayName="modules" class="modules-list">
            <div *ngFor="let module of modules.controls; let i = index" [formGroupName]="i" class="module-card">
              <div class="module-header">
                <h3>Module {{ i + 1 }}</h3>
                <button type="button" class="btn-icon btn-icon--danger" (click)="removeModule(i)" [disabled]="modules.length === 1">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                  </svg>
                </button>
              </div>

              <div class="form-grid">
                <div class="form-group">
                  <label class="form-label required">Module Name</label>
                  <input
                    type="text"
                    class="form-control"
                    formControlName="name"
                    placeholder="Enter module name"
                    [class.error]="module.get('name')?.invalid && module.get('name')?.touched">
                  <div class="form-error" *ngIf="module.get('name')?.invalid && module.get('name')?.touched">
                    <span *ngIf="module.get('name')?.errors?.['required']">Module name is required</span>
                  </div>
                </div>

                <div class="form-group form-group--full">
                  <label class="form-label">Module Description</label>
                  <textarea
                    class="form-control"
                    formControlName="description"
                    rows="2"
                    placeholder="Describe this module..."></textarea>
                </div>
              </div>

              <!-- Features for this module -->
              <div class="features-section">
                <div class="features-header">
                  <h4>Features</h4>
                  <button type="button" class="btn btn-sm btn-outline" (click)="addFeature(i)">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                      <rect x="11" y="4" width="2" height="16" rx="1"></rect>
                      <rect x="4" y="11" width="16" height="2" rx="1"></rect>
                    </svg>
                    Add Feature
                  </button>
                </div>

                <div formArrayName="features" class="features-list">
                  <div *ngFor="let feature of getFeatures(i).controls; let j = index" [formGroupName]="j" class="feature-item">
                    <div class="feature-inputs">
                      <input
                        type="text"
                        class="form-control"
                        formControlName="name"
                        placeholder="Feature name"
                        [class.error]="feature.get('name')?.invalid && feature.get('name')?.touched">
                      <input
                        type="text"
                        class="form-control"
                        formControlName="description"
                        placeholder="Feature description (optional)">
                      <button type="button" class="btn-icon btn-icon--danger" (click)="removeFeature(i, j)" [disabled]="getFeatures(i).length === 1">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                        </svg>
                      </button>
                    </div>
                    <div class="form-error" *ngIf="feature.get('name')?.invalid && feature.get('name')?.touched">
                      <span *ngIf="feature.get('name')?.errors?.['required']">Feature name is required</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Team Members -->
        <div class="form-section">
          <div class="section-header">
            <h2>Team Members</h2>
            <p>Assign team members to this project</p>
          </div>

          <div class="team-selection">
            <div class="role-group" *ngFor="let role of userRoles">
              <h4>{{ role }}</h4>
              <div class="user-checkboxes">
                <label *ngFor="let user of getUsersByRole(role)" class="user-checkbox">
                  <input
                    type="checkbox"
                    [value]="user.id"
                    [checked]="isUserSelected(user.id)"
                    (change)="toggleUser(user.id, role, $event)">
                  <div class="user-info">
                    <div class="user-avatar">{{ getInitials(user.fullName) }}</div>
                    <div class="user-details">
                      <span class="user-name">{{ user.fullName }}</span>
                      <span class="user-email">{{ user.email }}</span>
                    </div>
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  `,
  styles: [`
    .project-form__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .project-form__header h1 {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: var(--spacing-3);
    }

    /* Form Sections */
    .form-section {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-6);
      margin-bottom: var(--spacing-6);
    }

    .section-header {
      margin-bottom: var(--spacing-6);
      padding-bottom: var(--spacing-4);
      border-bottom: 1px solid var(--color-gray-100);
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      gap: var(--spacing-4);
    }

    .section-header h2 {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0;
    }

    .section-header p {
      color: var(--color-gray-600);
      margin: var(--spacing-1) 0 0 0;
      font-size: var(--font-size-sm);
    }

    /* Form Grid */
    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-4);
    }

    .form-group--full {
      grid-column: 1 / -1;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .form-label {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
    }

    .form-label.required::after {
      content: ' *';
      color: var(--color-error-500);
    }

    .form-control {
      padding: var(--spacing-3);
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-md);
      font-size: var(--font-size-sm);
      transition: all var(--transition-fast);
    }

    .form-control:focus {
      outline: none;
      border-color: var(--color-primary-500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-control.error {
      border-color: var(--color-error-500);
    }

    .form-control.error:focus {
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }

    .form-error {
      font-size: var(--font-size-xs);
      color: var(--color-error-600);
      margin-top: var(--spacing-1);
    }

    /* Modules */
    .modules-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-6);
    }

    .module-card {
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-5);
      background: var(--color-gray-50);
    }

    .module-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-4);
    }

    .module-header h3 {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-800);
      margin: 0;
    }

    .btn-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-md);
      background: var(--color-white);
      color: var(--color-gray-600);
      cursor: pointer;
      transition: all var(--transition-fast);
    }

    .btn-icon:hover:not(:disabled) {
      border-color: var(--color-primary-300);
      color: var(--color-primary-600);
      background: var(--color-primary-50);
    }

    .btn-icon--danger:hover:not(:disabled) {
      border-color: var(--color-error-300);
      color: var(--color-error-600);
      background: var(--color-error-50);
    }

    .btn-icon:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    /* Features */
    .features-section {
      margin-top: var(--spacing-4);
      padding-top: var(--spacing-4);
      border-top: 1px solid var(--color-gray-200);
    }

    .features-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-3);
    }

    .features-header h4 {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
      margin: 0;
    }

    .features-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-3);
    }

    .feature-item {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-md);
      padding: var(--spacing-3);
    }

    .feature-inputs {
      display: grid;
      grid-template-columns: 1fr 1fr auto;
      gap: var(--spacing-3);
      align-items: center;
    }

    /* Team Selection */
    .team-selection {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-6);
    }

    .role-group h4 {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-800);
      margin: 0 0 var(--spacing-3) 0;
      padding-bottom: var(--spacing-2);
      border-bottom: 1px solid var(--color-gray-200);
    }

    .user-checkboxes {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: var(--spacing-3);
    }

    .user-checkbox {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      padding: var(--spacing-3);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-md);
      cursor: pointer;
      transition: all var(--transition-fast);
    }

    .user-checkbox:hover {
      border-color: var(--color-primary-300);
      background: var(--color-primary-50);
    }

    .user-checkbox input[type="checkbox"] {
      margin: 0;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      flex: 1;
    }

    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: var(--border-radius-full);
      background: var(--color-primary-100);
      color: var(--color-primary-700);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
    }

    .user-details {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);
    }

    .user-name {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
    }

    .user-email {
      font-size: var(--font-size-xs);
      color: var(--color-gray-600);
    }

    /* Responsive */
    @media (max-width: 768px) {
      .form-grid {
        grid-template-columns: 1fr;
      }

      .feature-inputs {
        grid-template-columns: 1fr;
      }

      .user-checkboxes {
        grid-template-columns: 1fr;
      }

      .section-header {
        flex-direction: column;
        align-items: flex-start;
      }
    }
  `]
})
export class ProjectFormComponent implements OnInit {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private projectService = inject(ProjectService);
  private userService = inject(UserService);

  projectForm!: FormGroup;
  isEditMode = false;
  projectId: string | null = null;
  saving = false;
  users: User[] = [];
  selectedTeamMembers: { userId: string; role: UserRole }[] = [];

  userRoles = Object.values(UserRole);

  ngOnInit() {
    this.initializeForm();
    this.loadUsers();
    this.checkEditMode();
  }

  private initializeForm() {
    this.projectForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      projectUrl: ['', [Validators.required, Validators.pattern(/^https?:\/\/.+/)]],
      modules: this.fb.array([this.createModuleGroup()])
    });
  }

  private createModuleGroup(): FormGroup {
    return this.fb.group({
      name: ['', Validators.required],
      description: [''],
      features: this.fb.array([this.createFeatureGroup()])
    });
  }

  private createFeatureGroup(): FormGroup {
    return this.fb.group({
      name: ['', Validators.required],
      description: ['']
    });
  }

  private loadUsers() {
    this.users = this.userService.getAllUsers();
  }

  private checkEditMode() {
    this.projectId = this.route.snapshot.paramMap.get('id');
    if (this.projectId) {
      this.isEditMode = true;
      this.loadProject();
    }
  }

  private loadProject() {
    if (!this.projectId) return;

    this.projectService.getProjectById(this.projectId).subscribe({
      next: (project) => {
        if (project) {
          this.populateForm(project);
        }
      },
      error: (error) => {
        console.error('Error loading project:', error);
        this.router.navigate(['/projects']);
      }
    });
  }

  private populateForm(project: Project) {
    this.projectForm.patchValue({
      name: project.name,
      description: project.description,
      projectUrl: project.projectUrl
    });

    // Clear existing modules and add project modules
    const modulesArray = this.modules;
    modulesArray.clear();

    project.modules.forEach(module => {
      const moduleGroup = this.createModuleGroup();
      moduleGroup.patchValue({
        name: module.name,
        description: module.description
      });

      // Clear features and add module features
      const featuresArray = moduleGroup.get('features') as FormArray;
      featuresArray.clear();

      module.features.forEach(feature => {
        const featureGroup = this.createFeatureGroup();
        featureGroup.patchValue({
          name: feature.name,
          description: feature.description
        });
        featuresArray.push(featureGroup);
      });

      modulesArray.push(moduleGroup);
    });

    // Set selected team members
    this.selectedTeamMembers = project.teamMembers.map(member => ({
      userId: member.userId,
      role: member.role
    }));
  }

  get modules(): FormArray {
    return this.projectForm.get('modules') as FormArray;
  }

  getFeatures(moduleIndex: number): FormArray {
    return this.modules.at(moduleIndex).get('features') as FormArray;
  }

  addModule() {
    this.modules.push(this.createModuleGroup());
  }

  removeModule(index: number) {
    if (this.modules.length > 1) {
      this.modules.removeAt(index);
    }
  }

  addFeature(moduleIndex: number) {
    const features = this.getFeatures(moduleIndex);
    features.push(this.createFeatureGroup());
  }

  removeFeature(moduleIndex: number, featureIndex: number) {
    const features = this.getFeatures(moduleIndex);
    if (features.length > 1) {
      features.removeAt(featureIndex);
    }
  }

  getUsersByRole(role: UserRole): User[] {
    return this.users.filter(user => user.role === role);
  }

  isUserSelected(userId: string): boolean {
    return this.selectedTeamMembers.some(member => member.userId === userId);
  }

  toggleUser(userId: string, role: UserRole, event: any) {
    if (event.target.checked) {
      this.selectedTeamMembers.push({ userId, role });
    } else {
      this.selectedTeamMembers = this.selectedTeamMembers.filter(
        member => member.userId !== userId
      );
    }
  }

  getInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  }

  onSubmit() {
    if (this.projectForm.invalid) {
      this.markFormGroupTouched(this.projectForm);
      return;
    }

    this.saving = true;
    const formValue = this.projectForm.value;

    const projectData = {
      name: formValue.name,
      description: formValue.description,
      projectUrl: formValue.projectUrl,
      modules: formValue.modules,
      teamMembers: this.selectedTeamMembers
    };

    if (this.isEditMode && this.projectId) {
      this.updateProject(projectData);
    } else {
      this.createProject(projectData);
    }
  }

  private createProject(projectData: CreateProjectRequest) {
    this.projectService.createProject(projectData).subscribe({
      next: (response) => {
        this.saving = false;
        if (response.data) {
          this.router.navigate(['/projects', response.data.id]);
        }
      },
      error: (error) => {
        console.error('Error creating project:', error);
        this.saving = false;
      }
    });
  }

  private updateProject(projectData: UpdateProjectRequest) {
    if (!this.projectId) return;

    this.projectService.updateProject(this.projectId, projectData).subscribe({
      next: (response) => {
        this.saving = false;
        this.router.navigate(['/projects', this.projectId]);
      },
      error: (error) => {
        console.error('Error updating project:', error);
        this.saving = false;
      }
    });
  }

  cancel() {
    this.router.navigate(['/projects']);
  }

  private markFormGroupTouched(formGroup: FormGroup | FormArray) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup || control instanceof FormArray) {
        this.markFormGroupTouched(control);
      } else {
        control?.markAsTouched();
      }
    });
  }
}
