import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { UserPreferences } from '../models/user.model';

export type ViewMode = 'grid' | 'table';
export type Theme = 'light' | 'dark' | 'system';
export type DashboardLayout = 'grid' | 'list';

@Injectable({
  providedIn: 'root'
})
export class UserPreferencesService {
  private readonly STORAGE_KEY = 'bugtracker_user_preferences';
  
  private defaultPreferences: UserPreferences = {
    theme: 'light',
    language: 'en',
    timezone: 'UTC',
    emailNotifications: true,
    browserNotifications: true,
    dashboardLayout: 'grid',
    projectsViewMode: 'grid'
  };

  private preferencesSubject = new BehaviorSubject<UserPreferences>(this.defaultPreferences);
  public preferences$ = this.preferencesSubject.asObservable();

  constructor() {
    this.loadPreferences();
  }

  /**
   * Get current preferences
   */
  getPreferences(): UserPreferences {
    return this.preferencesSubject.value;
  }

  /**
   * Get preferences as observable
   */
  getPreferences$(): Observable<UserPreferences> {
    return this.preferences$;
  }

  /**
   * Update preferences (partial update)
   */
  updatePreferences(updates: Partial<UserPreferences>): void {
    const currentPreferences = this.preferencesSubject.value;
    const updatedPreferences = { ...currentPreferences, ...updates };
    
    this.preferencesSubject.next(updatedPreferences);
    this.savePreferences(updatedPreferences);
  }

  /**
   * Get specific preference value
   */
  getPreference<K extends keyof UserPreferences>(key: K): UserPreferences[K] {
    return this.preferencesSubject.value[key];
  }

  /**
   * Set specific preference value
   */
  setPreference<K extends keyof UserPreferences>(key: K, value: UserPreferences[K]): void {
    const updates = { [key]: value } as Partial<UserPreferences>;
    this.updatePreferences(updates);
  }

  /**
   * Get projects view mode
   */
  getProjectsViewMode(): ViewMode {
    return this.getPreference('projectsViewMode');
  }

  /**
   * Set projects view mode
   */
  setProjectsViewMode(mode: ViewMode): void {
    this.setPreference('projectsViewMode', mode);
  }

  /**
   * Get dashboard layout
   */
  getDashboardLayout(): DashboardLayout {
    return this.getPreference('dashboardLayout');
  }

  /**
   * Set dashboard layout
   */
  setDashboardLayout(layout: DashboardLayout): void {
    this.setPreference('dashboardLayout', layout);
  }

  /**
   * Get theme preference
   */
  getTheme(): Theme {
    return this.getPreference('theme');
  }

  /**
   * Set theme preference
   */
  setTheme(theme: Theme): void {
    this.setPreference('theme', theme);
  }

  /**
   * Reset preferences to default
   */
  resetPreferences(): void {
    this.preferencesSubject.next(this.defaultPreferences);
    this.savePreferences(this.defaultPreferences);
  }

  /**
   * Load preferences from localStorage
   */
  private loadPreferences(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const preferences = JSON.parse(stored) as UserPreferences;
        // Merge with defaults to ensure all properties exist
        const mergedPreferences = { ...this.defaultPreferences, ...preferences };
        this.preferencesSubject.next(mergedPreferences);
      }
    } catch (error) {
      console.warn('Failed to load user preferences from localStorage:', error);
      // Use default preferences if loading fails
      this.preferencesSubject.next(this.defaultPreferences);
    }
  }

  /**
   * Save preferences to localStorage
   */
  private savePreferences(preferences: UserPreferences): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(preferences));
    } catch (error) {
      console.warn('Failed to save user preferences to localStorage:', error);
    }
  }

  /**
   * Clear preferences from localStorage
   */
  clearStoredPreferences(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      this.preferencesSubject.next(this.defaultPreferences);
    } catch (error) {
      console.warn('Failed to clear user preferences from localStorage:', error);
    }
  }

  /**
   * Check if preferences are stored
   */
  hasStoredPreferences(): boolean {
    try {
      return localStorage.getItem(this.STORAGE_KEY) !== null;
    } catch (error) {
      return false;
    }
  }
}
